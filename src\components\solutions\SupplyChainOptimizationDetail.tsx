import React from "react";
import { Link } from "react-router-dom";
import {
  CheckCircle,
  TrendingUp,
  Clock,
  Users,
  ArrowRight,
  Database,
  Target,
  Zap,
  ExternalLink,
  Check,
} from "lucide-react";

const SupplyChainOptimizationDetail: React.FC = () => {
  return (
    <div className="relative overflow-hidden">
      {/* Banner部分 */}
      <section className="bg-gradient-to-br from-theme-900 via-theme-800 to-secondary-900 text-white pt-24 pb-16 relative overflow-hidden">
        {/* 简约背景装饰 */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-64 h-64 bg-gradient-to-br from-theme-600/10 to-transparent rounded-full -translate-x-1/2 -translate-y-1/2" />
          <div className="absolute bottom-0 right-0 w-64 h-64 bg-gradient-to-tl from-secondary-600/10 to-transparent rounded-full translate-x-1/2 translate-y-1/2" />
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center pt-10">
            <h1 className="text-4xl lg:text-5xl font-bold mb-6">
              经营端 - 供应链协同优化智能决策解决方案
            </h1>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
              通过数据融合与实时决策，实现多目标全局优化，提升供应链协同效率
            </p>
          </div>
        </div>
      </section>

      {/* 痛点挑战 */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">痛点挑战</h2>
            <div className="w-16 h-1 bg-theme-600 mx-auto mb-6 rounded-full" />
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              供应链协同优化面临的核心挑战与痛点
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* 痛点1：数据孤岛与整合难 */}
            <div className="bg-white rounded-xl p-6 shadow-lg border border-red-100 hover:shadow-xl transition-shadow duration-300">
              <div className="w-14 h-14 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Database className="h-10 w-10 text-red-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2 text-center">
                数据孤岛与整合难
              </h3>
              <p className="text-gray-600 text-center text-sm mb-4">
                生产数据（MES）、订单数据（ERP）、人力数据（HR系统）分散，人工整合耗时长且易出错，排产依赖不完整数据，导致计划偏离实际。
              </p>
            </div>

            {/* 痛点2：多目标冲突与全局优化难 */}
            <div className="bg-white rounded-xl p-6 shadow-lg border border-orange-100 hover:shadow-xl transition-shadow duration-300">
              <div className="w-14 h-14 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Target className="h-10 w-10 text-orange-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2 text-center">
                多目标冲突与全局优化难
              </h3>
              <p className="text-gray-600 text-center text-sm mb-4">
                需同时满足交期、成本、设备利用率、员工满意度等目标，人工难以量化平衡，为保交付牺牲成本，或为省成本延误订单。
              </p>
            </div>

            {/* 痛点3：动态扰动响应滞后 */}
            <div className="bg-white rounded-xl p-6 shadow-lg border border-yellow-100 hover:shadow-xl transition-shadow duration-300">
              <div className="w-14 h-14 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="h-10 w-10 text-yellow-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2 text-center">
                动态扰动响应滞后
              </h3>
              <p className="text-gray-600 text-center text-sm mb-4">
                插单、设备故障、员工缺勤等突发变化需数小时甚至天级人工调整，计划僵化导致产能浪费率高。
              </p>
            </div>

            {/* 痛点4：人力技能与任务匹配低效 */}
            <div className="bg-white rounded-xl p-6 shadow-lg border border-blue-100 hover:shadow-xl transition-shadow duration-300">
              <div className="w-14 h-14 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="h-10 w-10 text-blue-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2 text-center">
                人力技能与任务匹配低效
              </h3>
              <p className="text-gray-600 text-center text-sm mb-4">
                员工技能等级、认证资质、疲劳度未被量化建模，任务分配凭经验，导致高技能员工做低附加值工作，或关键岗位人力不足。
              </p>
            </div>

            {/* 痛点5：长周期预测偏差大 */}
            <div className="bg-white rounded-xl p-6 shadow-lg border border-purple-100 hover:shadow-xl transition-shadow duration-300">
              <div className="w-14 h-14 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="h-10 w-10 text-purple-600" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2 text-center">
                长周期预测偏差大
              </h3>
              <p className="text-gray-600 text-center text-sm mb-4">
                传统统计模型难以捕捉市场波动、季节性需求，月计划准确率低，旺季人力不足、淡季设备闲置而导致的产能错配。
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 目标价值 */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">目标价值</h2>
            <div className="w-16 h-1 bg-theme-600 mx-auto mb-6 rounded-full" />
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              通过智能化技术实现供应链协同优化的核心价值
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* 数据融合与实时决策 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-green-100">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Database className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">
                数据融合与实时决策
              </h3>
              <p className="text-gray-600 text-center mb-6">
                构建智能化数据网络
              </p>

              <div className="space-y-3">
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-green-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">
                    通过知识图谱技术，构建设备-订单-人力关系网络
                  </span>
                </div>
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-green-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">
                    自动关联多源数据，消除数据孤岛
                  </span>
                </div>
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-green-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">
                    通过数字孪生技术，实时映射物理生产系统
                  </span>
                </div>
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-green-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">
                    仿真预演排产方案，提前识别风险
                  </span>
                </div>
              </div>
            </div>

            {/* 多目标全局优化 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-blue-100">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Target className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">
                多目标全局优化
              </h3>
              <p className="text-gray-600 text-center mb-6">智能平衡多重目标</p>

              <div className="space-y-3">
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-blue-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">
                    训练智能体在"减少加班"与"提升OEE"等目标间动态平衡
                  </span>
                </div>
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-blue-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">
                    输出成本、交期、能耗的帕累托最优解集
                  </span>
                </div>
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-blue-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">
                    量化决策依据，避免主观判断偏差
                  </span>
                </div>
              </div>
            </div>

            {/* 实时动态重调度 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-purple-100">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Zap className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">
                实时动态重调度
              </h3>
              <p className="text-gray-600 text-center mb-6">快速响应变化</p>

              <div className="space-y-3">
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-purple-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">每30分钟滚动优化未来4小时计划</span>
                </div>
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-purple-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">
                    秒级响应突发事件，自动调整排产
                  </span>
                </div>
                <div className="flex items-start text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-purple-500 flex-shrink-0 mt-0.5" />
                  <span className="text-sm">最大化产能利用率，减少浪费</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 应用案例 */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">应用案例</h2>
            <div className="w-16 h-1 bg-theme-600 mx-auto mb-6 rounded-full" />
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              真实案例展示供应链协同优化在复杂场景中的卓越表现
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* 案例1：精密仪器零部件 */}
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
              <div className="relative h-48">
                <img
                  src="https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg?auto=compress&cs=tinysrgb&w=800"
                  alt="精密仪器零部件智能排产"
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent" />
                <div className="absolute bottom-4 left-4 right-4 text-white">
                  <div className="inline-flex items-center px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-sm mb-2">
                    精细化制造排产
                  </div>
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  某精密仪器零部件智能排产项目
                </h3>
                <p className="text-theme-600 font-medium mb-4">
                  精细化制造排产
                </p>
                <div className="mb-6">
                  <p className="text-gray-600 text-sm leading-relaxed">
                    研发实时动态排产优化模型与算法，实现秒级排产响应速度，显著提升设备利用率、订单交付准时率、排产效率以及成本管理。
                  </p>
                </div>

                <Link
                  to="/cases?case=precision-parts-supply-chain"
                  className="group inline-flex items-center justify-center px-6 py-3 bg-theme-600 text-white font-medium rounded-lg hover:bg-theme-700 transition-all duration-300"
                >
                  <span>查看详情</span>
                  <ExternalLink className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Link>
              </div>
            </div>

            {/* 案例2：模具加工 */}
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
              <div className="relative h-48">
                <img
                  src="https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg?auto=compress&cs=tinysrgb&w=800"
                  alt="模具加工智能排产"
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent" />
                <div className="absolute bottom-4 left-4 right-4 text-white">
                  <div className="inline-flex items-center px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-sm mb-2">
                    大规模模具排产
                  </div>
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  某模具加工智能排产优化项目
                </h3>
                <p className="text-theme-600 font-medium mb-4">
                  大规模模具排产
                </p>
                <div className="mb-6">
                  <p className="text-gray-600 text-sm leading-relaxed">
                    基于工艺路线及资源池，将工序直接排配到具体的设备、人员及加工时间。求解GAP由原来的90%提升至0-20%，日常任务可在3分钟内获得最优解。
                  </p>
                </div>

                <Link
                  to="/cases?case=mold-processing-supply-chain"
                  className="group inline-flex items-center justify-center px-6 py-3 bg-theme-600 text-white font-medium rounded-lg hover:bg-theme-700 transition-all duration-300"
                >
                  <span>查看详情</span>
                  <ExternalLink className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Link>
              </div>
            </div>

            {/* 案例3：金属加工 */}
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
              <div className="relative h-48">
                <img
                  src="https://images.pexels.com/photos/3184339/pexels-photo-3184339.jpeg?auto=compress&cs=tinysrgb&w=800"
                  alt="金属加工企业工艺优化"
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent" />
                <div className="absolute bottom-4 left-4 right-4 text-white">
                  <div className="inline-flex items-center px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-sm mb-2">
                    工艺优化
                  </div>
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  某金属加工企业工艺优化项目
                </h3>
                <p className="text-theme-600 font-medium mb-4">工艺优化</p>
                <div className="mb-6">
                  <p className="text-gray-600 text-sm leading-relaxed">
                    综合考虑目前所有箔轧库存、在制品库存、订单规格需求，在5-10分钟内快速获得全局最优分切方案，最大化提升箔轧的成材率。
                  </p>
                </div>

                <Link
                  to="/cases?case=metal-processing-optimization"
                  className="group inline-flex items-center justify-center px-6 py-3 bg-theme-600 text-white font-medium rounded-lg hover:bg-theme-700 transition-all duration-300"
                >
                  <span>查看详情</span>
                  <ExternalLink className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default SupplyChainOptimizationDetail;
